package mesfavoris.internal.settings;

import mesfavoris.placeholders.PathPlaceholder;
import org.junit.Test;

import java.nio.file.Paths;

import static org.junit.Assert.*;

/**
 * Test documenting the expected behavior for placeholder editing
 */
public class PlaceholderEditBehaviorTest {

    // Note: UI dialog tests are commented out due to headless test environment
    // The dialog behavior is tested manually and through integration tests

    @Test
    public void testPlaceholderNameImmutabilityPrinciple() {
        // Given - an existing placeholder
        PathPlaceholder original = new PathPlaceholder("HOME", Paths.get("/home/<USER>"));
        
        // When - we want to "edit" it (in practice, only path should be editable)
        // This test documents the principle that names should not change to avoid breaking references
        
        // Then - the name should remain the same to preserve bookmark references
        assertEquals("Original name should be preserved", "HOME", original.getName());
        
        // Note: In the UI, the name field is disabled when editing existing placeholders
        // This prevents users from accidentally breaking bookmark references
    }

    @Test
    public void testPathOnlyModificationScenario() {
        // Given - a scenario where user moves their home directory
        PathPlaceholder original = new PathPlaceholder("HOME", Paths.get("/home/<USER>"));
        
        // When - user updates the path (simulating what happens in the dialog)
        PathPlaceholder updated = new PathPlaceholder(original.getName(), Paths.get("/home/<USER>"));
        
        // Then - name stays the same, path is updated
        assertEquals("Name should remain unchanged", "HOME", updated.getName());
        assertEquals("Path should be updated", Paths.get("/home/<USER>").toAbsolutePath(), updated.getPath());
        
        // This preserves all existing bookmark references while updating the actual path
    }
}
