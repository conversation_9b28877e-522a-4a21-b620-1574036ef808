package mesfavoris.internal.settings.placeholders;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.ui.*;
import com.intellij.ui.components.JBList;
import com.intellij.util.ui.JBUI;
import mesfavoris.model.BookmarkFolder;
import mesfavoris.model.BookmarkId;
import mesfavoris.model.BookmarksTree;
import mesfavoris.path.PathBookmarkProperties;
import mesfavoris.placeholders.PathPlaceholder;
import mesfavoris.service.BookmarksService;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Panel to configure placeholders
 */
public class PlaceholdersPanel extends JPanel {
    private DefaultListModel<PathPlaceholder> listModel;
    private JBList<PathPlaceholder> list;
    private JPanel mainPanel;
    private PathPlaceholderStats stats;

    public PlaceholdersPanel() {
        this(null);
    }

    public PlaceholdersPanel(@Nullable Project project) {
        super(new BorderLayout());
        initComponents(project);
        layoutComponents();
    }

    private void initComponents() {
        // Initialize stats with BookmarksService and file path property
        List<String> pathPropertyNames = Arrays.asList(PathBookmarkProperties.PROP_FILE_PATH);

        try {
            Project project = ProjectManager.getInstance().getDefaultProject();
            if (project != null) {
                BookmarksService bookmarksService = project.getService(BookmarksService.class);
                if (bookmarksService != null) {
                    stats = new PathPlaceholderStats(() -> bookmarksService.getBookmarksTree(), pathPropertyNames);
                } else {
                    throw new RuntimeException("BookmarksService not available");
                }
            } else {
                throw new RuntimeException("Default project not available");
            }
        } catch (Exception e) {
            // Fallback for test environment - create stats with empty bookmarks tree
            BookmarkFolder rootFolder = new BookmarkFolder(new BookmarkId("root"), "Root");
            BookmarksTree emptyTree = new BookmarksTree(rootFolder);
            stats = new PathPlaceholderStats(() -> emptyTree, pathPropertyNames);
        }

        listModel = new DefaultListModel<>();
        list = new JBList<>(listModel);
        list.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // Custom cell renderer to show placeholder name, usage count, and path
        list.setCellRenderer(new PlaceholderListCellRenderer());

        // Add double-click listener to edit placeholders
        new DoubleClickListener() {
            @Override
            protected boolean onDoubleClick(@NotNull MouseEvent event) {
                int selectedIndex = list.getSelectedIndex();
                if (selectedIndex >= 0) {
                    editPlaceholder(selectedIndex);
                    return true;
                }
                return false;
            }
        }.installOn(list);
    }

    private void layoutComponents() {
        ToolbarDecorator decorator = ToolbarDecorator.createDecorator(list)
            .setAddAction(button -> addPlaceholder())
            .setEditAction(button -> {
                int selectedIndex = list.getSelectedIndex();
                if (selectedIndex >= 0) {
                    editPlaceholder(selectedIndex);
                }
            })
            .setRemoveAction(button -> {
                int selectedIndex = list.getSelectedIndex();
                if (selectedIndex >= 0) {
                    removePlaceholder(selectedIndex);
                }
            })
            .setEditActionUpdater(e -> list.getSelectedIndex() >= 0)
            .setRemoveActionUpdater(e -> list.getSelectedIndex() >= 0);

        mainPanel = decorator.createPanel();
        add(mainPanel, BorderLayout.CENTER);

        // Add explanatory label
        JLabel helpLabel = new JLabel("<html><body style='width: 400px'>" +
            "Placeholders allow you to define shortcuts for frequently used paths. " +
            "For example, a 'HOME' placeholder pointing to '/home/<USER>' will allow you to use '${HOME}/documents' " +
            "in bookmarks." +
            "</body></html>");
        helpLabel.setBorder(JBUI.Borders.emptyBottom(10));
        add(helpLabel, BorderLayout.NORTH);
    }

    public void setPlaceholders(List<PathPlaceholder> placeholders) {
        listModel.clear();
        for (PathPlaceholder placeholder : placeholders) {
            listModel.addElement(placeholder);
        }
    }

    public List<PathPlaceholder> getPlaceholders() {
        List<PathPlaceholder> result = new ArrayList<>();
        for (int i = 0; i < listModel.getSize(); i++) {
            result.add(listModel.getElementAt(i));
        }
        return result;
    }

    public boolean isModified(List<PathPlaceholder> originalPlaceholders) {
        List<PathPlaceholder> currentPlaceholders = getPlaceholders();
        if (currentPlaceholders.size() != originalPlaceholders.size()) {
            return true;
        }

        for (int i = 0; i < currentPlaceholders.size(); i++) {
            PathPlaceholder current = currentPlaceholders.get(i);
            PathPlaceholder original = originalPlaceholders.get(i);

            if (!current.equals(original)) {
                return true;
            }
        }

        return false;
    }

    private void addPlaceholder() {
        Project project = ProjectManager.getInstance().getDefaultProject();
        List<PathPlaceholder> existingPlaceholders = getPlaceholders();
        PlaceholderEditDialog dialog = new PlaceholderEditDialog(project, existingPlaceholders);

        if (dialog.showAndGet()) {
            PathPlaceholder newPlaceholder = dialog.getPlaceholder();
            if (newPlaceholder != null) {
                listModel.addElement(newPlaceholder);
                int newIndex = listModel.getSize() - 1;
                list.setSelectedIndex(newIndex);
            }
        }
    }

    private void editPlaceholder(int selectedIndex) {
        PathPlaceholder currentPlaceholder = listModel.getElementAt(selectedIndex);
        if (currentPlaceholder != null) {
            Project project = ProjectManager.getInstance().getDefaultProject();
            List<PathPlaceholder> existingPlaceholders = getPlaceholders();
            PlaceholderEditDialog dialog = new PlaceholderEditDialog(project, currentPlaceholder, existingPlaceholders);

            if (dialog.showAndGet()) {
                PathPlaceholder updatedPlaceholder = dialog.getPlaceholder();
                if (updatedPlaceholder != null) {
                    listModel.setElementAt(updatedPlaceholder, selectedIndex);
                }
            }
        }
    }

    private void removePlaceholder(int selectedIndex) {
        listModel.removeElementAt(selectedIndex);
    }

    /**
     * Custom cell renderer for placeholder list items
     */
    private class PlaceholderListCellRenderer extends ColoredListCellRenderer<PathPlaceholder> {
        @Override
        protected void customizeCellRenderer(@NotNull JList<? extends PathPlaceholder> list, PathPlaceholder placeholder,
                                           int index, boolean selected, boolean hasFocus) {
            if (placeholder != null) {
                // Get usage count from stats
                int usageCount = stats.getUsageCount(placeholder.getName());

                // Placeholder name
                append(placeholder.getName(), SimpleTextAttributes.REGULAR_BOLD_ATTRIBUTES);

                // Usage count in different color
                if (usageCount > 0) {
                    append(" (" + usageCount + " matches)", SimpleTextAttributes.GRAYED_ATTRIBUTES);
                } else {
                    append(" (unused)", SimpleTextAttributes.GRAYED_ATTRIBUTES);
                }

                // Path
                append(" - " + placeholder.getPath().toString(), SimpleTextAttributes.REGULAR_ATTRIBUTES);
            }
        }
    }

}
