<idea-plugin>
    <extensions defaultExtensionNs="com.intellij">
        <toolWindow id="mesfavoris" secondary="true" icon="AllIcons.General.Modified" anchor="right"
                    factoryClass="mesfavoris.internal.toolwindow.MesFavorisToolWindowFactory"/>
        <postStartupActivity implementation="mesfavoris.internal.markers.BookmarksHighlighters$BookmarksHighlightersStartupActivity"/>
        <notificationGroup
                id="com.cchabanois.mesfavoris.errors"
                displayType="STICKY_BALLOON"
                isLogByDefault="true"
        />
        <notificationGroup
                id="com.cchabanois.mesfavoris.info"
                displayType="BALLOON"
                isLogByDefault="false"
        />
        <projectService serviceImplementation="mesfavoris.internal.toolwindow.BookmarksTreeComponentStateService"/>
    </extensions>
</idea-plugin>